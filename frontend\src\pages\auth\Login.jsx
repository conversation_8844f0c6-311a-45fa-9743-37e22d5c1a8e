import { useState, useEffect } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  FormControlLabel,
  Checkbox,
  Link,
  Grid,
  Typography,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  FormControl,
  InputLabel,
  OutlinedInput,
  Paper,
  Divider,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Person as PersonIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Login as LoginIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useLocalization } from '../../contexts/LocalizationContext';
import TranslationDebug from '../../components/TranslationDebug';
import SimpleTranslationTest from '../../components/SimpleTranslationTest';

const Login = () => {
  const navigate = useNavigate();
  const { login, error } = useAuth();
  const { t } = useLocalization();
  const theme = useTheme();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formError, setFormError] = useState('');
  const [animate, setAnimate] = useState(false);

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setAnimate(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!username || !password) {
      setFormError(t('please_fill_required_fields', 'Please fill in all required fields'));
      return;
    }

    try {
      setLoading(true);
      setFormError('');
      await login(username, password);

      // If remember me is checked, we could set a longer expiration for the token
      // or store additional info in localStorage
      if (rememberMe) {
        localStorage.setItem('rememberMe', 'true');
      } else {
        localStorage.removeItem('rememberMe');
      }

      navigate('/dashboard');
    } catch (err) {
      setFormError(err.response?.data?.detail || t('login_failed_check_credentials', 'Login failed. Please check your credentials.'));
    } finally {
      setLoading(false);
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      noValidate
      sx={{
        width: '100%',
        position: 'relative',
        transition: 'all 0.3s ease-in-out',
        transform: animate ? 'translateY(0)' : 'translateY(20px)',
        opacity: animate ? 1 : 0,
      }}
    >
      {(formError || error) && (
        <Alert
          severity="error"
          sx={{
            mb: 2, // Reduced from 4 to 2
            borderRadius: 2,
            boxShadow: theme.shadows[2],
            animation: 'shake 0.5s cubic-bezier(.36,.07,.19,.97) both',
            '@keyframes shake': {
              '10%, 90%': { transform: 'translate3d(-1px, 0, 0)' },
              '20%, 80%': { transform: 'translate3d(2px, 0, 0)' },
              '30%, 50%, 70%': { transform: 'translate3d(-4px, 0, 0)' },
              '40%, 60%': { transform: 'translate3d(4px, 0, 0)' },
            }
          }}
        >
          {formError || error}
        </Alert>
      )}

      <Paper
        elevation={0}
        sx={{
          p: 2, // Reduced from 3 to 2
          mb: 2, // Reduced from 4 to 2
          borderRadius: 2, // Reduced from 3 to 2
          background: theme.palette.mode === 'light'
            ? alpha(theme.palette.primary.main, 0.03)
            : alpha(theme.palette.primary.main, 0.05),
          border: `1px solid ${theme.palette.mode === 'light'
            ? alpha(theme.palette.primary.main, 0.1)
            : alpha(theme.palette.primary.main, 0.2)}`,
        }}
      >
        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
          {t('email_username', 'Email / Username')} <Box component="span" sx={{ color: 'error.main' }}>*</Box>
        </Typography>
        <TextField
          fullWidth
          id="username"
          name="username"
          autoComplete="username"
          autoFocus
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          placeholder={t('admin_goid_com', 'admin@goid.<NAME_EMAIL>')}
          sx={{
            mb: 2, // Reduced from 3 to 2
            '& .MuiOutlinedInput-root': {
              transition: 'all 0.2s ease-in-out',
              '&:hover, &.Mui-focused': {
                boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`,
              }
            }
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <PersonIcon color="action" />
              </InputAdornment>
            ),
          }}
        />

        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
          {t('password', 'Password')} <Box component="span" sx={{ color: 'error.main' }}>*</Box>
        </Typography>
        <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}> {/* Reduced from 3 to 2 */}
          <OutlinedInput
            id="password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder={t('enter_password', 'Enter your password')}
            sx={{
              transition: 'all 0.2s ease-in-out',
              '&:hover, &.Mui-focused': {
                boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`,
              }
            }}
            startAdornment={
              <InputAdornment position="start">
                <LockIcon color="action" />
              </InputAdornment>
            }
            endAdornment={
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleClickShowPassword}
                  edge="end"
                >
                  {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              </InputAdornment>
            }
          />
        </FormControl>

        <Grid container justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}> {/* Reduced from 3 to 2 */}
          <Grid item>
            <FormControlLabel
              control={
                <Checkbox
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  color="primary"
                  sx={{
                    '&.Mui-checked': {
                      color: theme.palette.primary.main,
                    }
                  }}
                />
              }
              label={<Typography variant="body2">{t('remember_me', 'Remember Me')}</Typography>}
            />
          </Grid>
          <Grid item>
            <Link
              component={RouterLink}
              to="/forgot-password"
              variant="body2"
              sx={{
                color: 'primary.main',
                fontWeight: 500,
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline',
                }
              }}
            >
              {t('forgot_password', 'Forgot password?')}
            </Link>
          </Grid>
        </Grid>

        <Button
          type="submit"
          fullWidth
          variant="contained"
          sx={{
            mt: 1,
            mb: 2, // Reduced from 3 to 2
            py: 1.2, // Reduced from 1.5 to 1.2
            fontWeight: 600,
            position: 'relative',
            overflow: 'hidden',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '100%',
              background: `linear-gradient(90deg, transparent, ${alpha('#fff', 0.2)}, transparent)`,
              transition: 'all 0.5s ease',
            },
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: `0 8px 20px -6px ${alpha(theme.palette.primary.main, 0.5)}`,
              '&::after': {
                left: '100%',
              }
            }
          }}
          disabled={loading}
          startIcon={loading ? null : <LoginIcon />}
        >
          {loading ? <CircularProgress size={24} /> : t('sign_in', 'Sign In')}
        </Button>
      </Paper>

      <Box sx={{ textAlign: 'center', mt: 1 }}> {/* Reduced from 2 to 1 */}
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontStyle: 'italic' }}>
          {t('tenant_redirect_notice', 'Tenant users will be automatically directed to their tenant based on the domain.')}
        </Typography>
      </Box>
      <TranslationDebug />
      <SimpleTranslationTest />
    </Box>
  );
};

export default Login;
