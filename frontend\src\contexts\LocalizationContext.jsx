/**
 * Localization Context for GoID System
 * Provides translation and language switching functionality
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import localizationService from '../services/localizationService';

const LocalizationContext = createContext();

export const useLocalization = () => {
  const context = useContext(LocalizationContext);
  if (!context) {
    throw new Error('useLocalization must be used within a LocalizationProvider');
  }
  return context;
};

export const LocalizationProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(localizationService.getCurrentLanguage());
  const [isLoading, setIsLoading] = useState(true);
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);
  const [translations, setTranslations] = useState({}); // Store translations in state
  const [translationVersion, setTranslationVersion] = useState(0); // Force re-renders

  // Initialize localization on mount
  useEffect(() => {
    const initializeLocalization = async () => {
      setIsLoading(true);
      try {
        await localizationService.initialize();
        const language = localizationService.getCurrentLanguage();
        setCurrentLanguage(language);
        
        // Load current language translations into state  
        const initialTranslations = localizationService.translations[language] || {};
        console.log(`🚀 Initializing with ${Object.keys(initialTranslations).length} translations for ${language}:`, initialTranslations);
        setTranslations(initialTranslations);
      } catch (error) {
        console.error('Failed to initialize localization:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLocalization();
  }, []);

  // Update document direction when language changes
  useEffect(() => {
    document.documentElement.dir = localizationService.getDirection();
    document.documentElement.lang = currentLanguage;
  }, [currentLanguage]);

  /**
   * Change language
   */
  const changeLanguage = async (languageCode) => {
    if (languageCode === currentLanguage) {
      return true;
    }

    console.log(`🔄 Changing language from ${currentLanguage} to ${languageCode}`);
    setIsChangingLanguage(true);
    try {
      const success = await localizationService.setLanguage(languageCode);
      if (success || true) { // Accept both success and fallback
        setCurrentLanguage(languageCode);
        
        // Get translations from service after language change
        const newTranslations = localizationService.translations[languageCode] || {};
        console.log(`📚 Setting ${Object.keys(newTranslations).length} translations in state for ${languageCode}:`, newTranslations);
        
        // Update translations in state immediately for re-rendering
        setTranslations(newTranslations);
        setTranslationVersion(prev => prev + 1); // Force re-render
        
        console.log(`✅ Language changed to ${languageCode}, state updated with translations`);
        
        // Show success message
        if (window.showNotification) {
          window.showNotification(
            localizationService.t('language_changed', 'Language changed successfully'),
            'success'
          );
        }
        
        return true;
      } else {
        console.warn('Language change failed');
        return false;
      }
    } catch (error) {
      console.error('Failed to change language:', error);
      return false;
    } finally {
      setIsChangingLanguage(false);
    }
  };

  /**
   * Get translation for a key
   */
  const t = (key, defaultValue = null) => {
    // ALWAYS use translations from React state first (reactive)
    const translation = translations[key];
    if (translation) {
      console.log(`✅ Translation found in state: ${key} = "${translation}" (lang: ${currentLanguage})`);
      return translation;
    }
    
    // If not in state, log and return default
    console.log(`❌ Translation missing in state: ${key}, using default: "${defaultValue}" (lang: ${currentLanguage})`);
    console.log(`🔍 Available translations:`, Object.keys(translations).slice(0, 10), '...');
    return defaultValue || key;
  };

  /**
   * Get translation with parameters
   */
  const tp = (key, params = {}, defaultValue = null) => {
    return localizationService.tp(key, params, defaultValue);
  };

  /**
   * Get available languages
   */
  const getAvailableLanguages = () => {
    return localizationService.getAvailableLanguages();
  };

  /**
   * Check if current language is RTL
   */
  const isRTL = () => {
    return localizationService.isRTL();
  };

  /**
   * Get language direction
   */
  const getDirection = () => {
    return localizationService.getDirection();
  };

  /**
   * Get current language info
   */
  const getCurrentLanguageInfo = () => {
    const languages = getAvailableLanguages();
    return languages.find(lang => lang.code === currentLanguage) || languages[0];
  };

  const value = {
    // State
    currentLanguage,
    isLoading,
    isChangingLanguage,
    translationVersion, // Include for dependency tracking
    
    // Functions
    changeLanguage,
    t,
    tp,
    getAvailableLanguages,
    isRTL,
    getDirection,
    getCurrentLanguageInfo,
    
    // Convenience properties
    isAmharic: currentLanguage === 'am',
    isEnglish: currentLanguage === 'en',
    direction: getDirection(),
    languageInfo: getCurrentLanguageInfo()
  };

  return (
    <LocalizationContext.Provider value={value}>
      {children}
    </LocalizationContext.Provider>
  );
};

export default LocalizationContext;
